package com.demo.survey.repository;

import com.demo.survey.entity.Survey;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SurveyRepository extends JpaRepository<Survey, Integer> {

    List<Survey> findByStatusOrderByCreateTimeDesc(Integer status);

    @Query("SELECT s FROM Survey s WHERE s.surveyName LIKE %:keyword%")
    List<Survey> findByKeyword(@Param("keyword") String keyword);

    List<Survey> findByStatusInOrderByCreateTimeDesc(List<Integer> statuses);
}
