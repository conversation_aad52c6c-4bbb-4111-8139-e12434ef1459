package com.demo.survey.service;

import com.demo.survey.dto.SurveyConfigDTO;
import com.demo.survey.entity.SurveyConfig;
import com.demo.survey.repository.SurveyConfigRepository;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional
public class SurveyConfigService {

    @Autowired
    private SurveyConfigRepository surveyConfigRepository;

    public List<SurveyConfigDTO> getConfigsBySurveyId(Integer surveyId) {
        List<SurveyConfig> configs = surveyConfigRepository.findBySurveyIdOrderById(surveyId);
        return configs.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    public List<SurveyConfigDTO> getQuestionsBySurveyId(Integer surveyId) {
        List<SurveyConfig> questions = surveyConfigRepository.findBySurveyIdAndTypeOrderById(surveyId, SurveyConfig.TYPE_QUESTION);
        return questions.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    public List<SurveyConfigDTO> getOptionsBySurveyId(Integer surveyId) {
        List<SurveyConfig> options = surveyConfigRepository.findBySurveyIdAndTypeOrderById(surveyId, SurveyConfig.TYPE_OPTION);
        return options.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    public SurveyConfigDTO createConfig(SurveyConfigDTO configDTO) {
        SurveyConfig config = new SurveyConfig();
        BeanUtils.copyProperties(configDTO, config);
        SurveyConfig savedConfig = surveyConfigRepository.save(config);
        return convertToDTO(savedConfig);
    }

    public SurveyConfigDTO updateConfig(Integer id, SurveyConfigDTO configDTO) {
        Optional<SurveyConfig> existingConfig = surveyConfigRepository.findById(id);
        if (existingConfig.isPresent()) {
            SurveyConfig config = existingConfig.get();
            config.setContent(configDTO.getContent());
            config.setType(configDTO.getType());
            SurveyConfig savedConfig = surveyConfigRepository.save(config);
            return convertToDTO(savedConfig);
        }
        return null;
    }

    public boolean deleteConfig(Integer id) {
        if (surveyConfigRepository.existsById(id)) {
            surveyConfigRepository.deleteById(id);
            return true;
        }
        return false;
    }

    private SurveyConfigDTO convertToDTO(SurveyConfig config) {
        SurveyConfigDTO dto = new SurveyConfigDTO();
        BeanUtils.copyProperties(config, dto);
        return dto;
    }
}
