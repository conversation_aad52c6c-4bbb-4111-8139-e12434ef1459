package com.demo.survey.controller;

import com.demo.survey.dto.SurveyConfigDTO;
import com.demo.survey.service.SurveyConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/survey-configs")
@CrossOrigin(origins = "*")
public class SurveyConfigController {

    @Autowired
    private SurveyConfigService surveyConfigService;

    @GetMapping("/survey/{surveyId}")
    public ResponseEntity<List<SurveyConfigDTO>> getConfigsBySurveyId(@PathVariable Integer surveyId) {
        List<SurveyConfigDTO> configs = surveyConfigService.getConfigsBySurveyId(surveyId);
        return ResponseEntity.ok(configs);
    }

    @GetMapping("/survey/{surveyId}/questions")
    public ResponseEntity<List<SurveyConfigDTO>> getQuestionsBySurveyId(@PathVariable Integer surveyId) {
        List<SurveyConfigDTO> questions = surveyConfigService.getQuestionsBySurveyId(surveyId);
        return ResponseEntity.ok(questions);
    }

    @GetMapping("/survey/{surveyId}/options")
    public ResponseEntity<List<SurveyConfigDTO>> getOptionsBySurveyId(@PathVariable Integer surveyId) {
        List<SurveyConfigDTO> options = surveyConfigService.getOptionsBySurveyId(surveyId);
        return ResponseEntity.ok(options);
    }

    @GetMapping("/survey/{surveyId}/questions/type/{optionType}")
    public ResponseEntity<List<SurveyConfigDTO>> getQuestionsByType(
            @PathVariable Integer surveyId,
            @PathVariable Integer optionType) {
        List<SurveyConfigDTO> questions = surveyConfigService.getQuestionsByType(surveyId, optionType);
        return ResponseEntity.ok(questions);
    }

    @GetMapping("/question/{questionId}/options")
    public ResponseEntity<List<SurveyConfigDTO>> getOptionsByQuestionId(@PathVariable Integer questionId) {
        List<SurveyConfigDTO> options = surveyConfigService.getOptionsByQuestionId(questionId);
        return ResponseEntity.ok(options);
    }

    @PostMapping
    public ResponseEntity<SurveyConfigDTO> createConfig(@Valid @RequestBody SurveyConfigDTO configDTO) {
        SurveyConfigDTO createdConfig = surveyConfigService.createConfig(configDTO);
        return ResponseEntity.ok(createdConfig);
    }

    @PutMapping("/{id}")
    public ResponseEntity<SurveyConfigDTO> updateConfig(@PathVariable Integer id, @Valid @RequestBody SurveyConfigDTO configDTO) {
        SurveyConfigDTO updatedConfig = surveyConfigService.updateConfig(id, configDTO);
        if (updatedConfig != null) {
            return ResponseEntity.ok(updatedConfig);
        }
        return ResponseEntity.notFound().build();
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteConfig(@PathVariable Integer id) {
        boolean deleted = surveyConfigService.deleteConfig(id);
        if (deleted) {
            return ResponseEntity.ok().build();
        }
        return ResponseEntity.notFound().build();
    }
}
