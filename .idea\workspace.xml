<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="E:\apache-maven-3.6.1" />
        <option name="localRepository" value="E:\apache-maven-3.6.1\mvn-repo" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="E:\apache-maven-3.6.1\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="SurveyDemoApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="survey-demo" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.demo.survey.SurveyDemoApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
</project>