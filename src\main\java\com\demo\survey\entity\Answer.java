package com.demo.survey.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "answer")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Answer {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "respondent_id", nullable = false)
    private String respondentId;

    @Column(name = "answer_text", length = 2000)
    private String answerText;

    @Column(name = "selected_option_ids", length = 500)
    private String selectedOptionIds; // 存储选中的选项ID，用逗号分隔

    @Column(name = "created_time", nullable = false)
    private LocalDateTime createdTime;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "question_id", nullable = false)
    private Question question;

    @PrePersist
    protected void onCreate() {
        createdTime = LocalDateTime.now();
    }
}
