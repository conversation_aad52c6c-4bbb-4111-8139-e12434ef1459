# 调查问卷系统 Demo

这是一个基于Spring Boot 2.7.6和JDK 8的调查问卷系统演示项目。

## 项目结构

```
survey_demo/
├── src/
│   ├── main/
│   │   ├── java/com/demo/survey/
│   │   │   ├── SurveyDemoApplication.java     # 主启动类
│   │   │   ├── config/
│   │   │   │   └── DataSourceConfig.java      # 数据源配置
│   │   │   ├── entity/                        # 实体类
│   │   │   │   ├── Survey.java               # 问卷实体
│   │   │   │   ├── SurveyConfig.java         # 问卷配置实体
│   │   │   │   └── Answer.java               # 答案实体
│   │   │   ├── repository/                    # 数据访问层
│   │   │   │   ├── SurveyRepository.java
│   │   │   │   ├── SurveyConfigRepository.java
│   │   │   │   └── AnswerRepository.java
│   │   │   ├── service/                       # 业务逻辑层
│   │   │   │   ├── SurveyService.java
│   │   │   │   ├── SurveyConfigService.java
│   │   │   │   └── AnswerService.java
│   │   │   ├── controller/                    # 控制器层
│   │   │   │   ├── SurveyController.java
│   │   │   │   ├── SurveyConfigController.java
│   │   │   │   └── AnswerController.java
│   │   │   ├── dto/                          # 数据传输对象
│   │   │   │   ├── SurveyDTO.java
│   │   │   │   ├── SurveyConfigDTO.java
│   │   │   │   └── AnswerDTO.java
│   │   │   ├── common/                       # 通用类
│   │   │   │   └── ApiResponse.java
│   │   │   └── exception/                    # 异常处理
│   │   │       └── GlobalExceptionHandler.java
│   │   └── resources/
│   │       ├── application.yml               # 配置文件
│   │       └── static/
│   │           └── index.html               # 测试页面
│   └── test/
│       └── java/com/demo/survey/
│           └── SurveyDemoApplicationTests.java
└── pom.xml                                  # Maven配置文件
```

## 数据库表结构

### 1. survey (问卷调查表)
- `id` - 主键
- `survey_name` - 问卷名
- `status` - 状态 (1启用, 2停用)
- `create_time` - 创建时间
- `update_time` - 更新时间

### 2. survey_config (问卷配置表)
- `id` - 主键
- `survey_id` - 调查问卷id
- `config_type` - 类型 (1问题, 2选项)
- `option_type` - 问题类型 (1填空, 2单选, 3多选)
- `content` - 内容
- `create_time` - 创建时间
- `update_time` - 更新时间

### 3. answer (问卷答案表)
- `id` - 主键
- `survey_id` - 问卷id
- `user_id` - 用户id
- `question_id` - 问题id
- `answer` - 答案
- `create_time` - 创建时间
- `update_time` - 更新时间

## 技术栈

- **Java**: JDK 8
- **框架**: Spring Boot 2.7.6
- **数据库**: MySQL 8.0
- **ORM**: Spring Data JPA
- **构建工具**: Maven
- **其他**: Lombok, Validation

## 配置说明

数据库连接配置在 `application.yml` 中：

```yaml
spring:
  datasource:
    jdbc-url: *********************************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
```

## API 接口

### 问卷管理 (/api/surveys)
- `GET /api/surveys` - 获取所有问卷
- `GET /api/surveys/status/{status}` - 根据状态获取问卷
- `GET /api/surveys/{id}` - 根据ID获取问卷
- `POST /api/surveys` - 创建问卷
- `PUT /api/surveys/{id}` - 更新问卷
- `DELETE /api/surveys/{id}` - 删除问卷
- `GET /api/surveys/search?keyword=xxx` - 搜索问卷

### 问卷配置管理 (/api/survey-configs)
- `GET /api/survey-configs/survey/{surveyId}` - 获取问卷的所有配置
- `GET /api/survey-configs/survey/{surveyId}/questions` - 获取问卷的问题
- `GET /api/survey-configs/survey/{surveyId}/options` - 获取问卷的选项
- `GET /api/survey-configs/survey/{surveyId}/questions/type/{optionType}` - 根据问题类型获取问题
- `POST /api/survey-configs` - 创建配置
- `PUT /api/survey-configs/{id}` - 更新配置
- `DELETE /api/survey-configs/{id}` - 删除配置

### 答案管理 (/api/answers)
- `GET /api/answers/survey/{surveyId}` - 获取问卷的所有答案
- `GET /api/answers/user/{userId}` - 获取用户的所有答案
- `GET /api/answers/survey/{surveyId}/user/{userId}` - 获取用户在特定问卷的答案
- `POST /api/answers` - 提交答案
- `PUT /api/answers/{id}` - 更新答案
- `DELETE /api/answers/{id}` - 删除答案
- `GET /api/answers/survey/{surveyId}/participants/count` - 获取问卷参与人数

## 运行方式

1. 确保MySQL数据库已启动并创建了相应的数据库
2. 修改 `application.yml` 中的数据库连接信息
3. 运行主类 `SurveyDemoApplication`
4. 访问 `http://localhost:8080` 查看测试页面
5. 使用 `http://localhost:8080/api/` 开头的接口进行API测试

## 测试

项目包含了一个简单的HTML测试页面，可以通过浏览器访问 `http://localhost:8080` 进行功能测试。

## 注意事项

1. 确保数据库连接信息正确
2. 数据库表会通过JPA自动创建
3. 项目使用了Lombok，需要IDE支持
4. 所有接口都支持跨域访问
