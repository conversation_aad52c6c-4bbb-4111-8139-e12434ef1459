package com.demo.survey.repository;

import com.demo.survey.entity.SurveyConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SurveyConfigRepository extends JpaRepository<SurveyConfig, Integer> {

    List<SurveyConfig> findBySurveyIdAndConfigTypeOrderById(Integer surveyId, Integer configType);

    List<SurveyConfig> findBySurveyIdOrderById(Integer surveyId);

    List<SurveyConfig> findByConfigTypeOrderById(Integer configType);

    List<SurveyConfig> findBySurveyIdAndConfigTypeAndOptionTypeOrderById(Integer surveyId, Integer configType, Integer optionType);
}
