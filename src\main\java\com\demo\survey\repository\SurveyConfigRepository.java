package com.demo.survey.repository;

import com.demo.survey.entity.SurveyConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SurveyConfigRepository extends JpaRepository<SurveyConfig, Integer> {

    List<SurveyConfig> findBySurveyIdAndTypeOrderById(Integer surveyId, Integer type);

    List<SurveyConfig> findBySurveyIdOrderById(Integer surveyId);

    List<SurveyConfig> findByTypeOrderById(Integer type);
}
