package com.demo.survey.controller;

import com.demo.survey.dto.SurveyDTO;
import com.demo.survey.service.SurveyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/surveys")
@CrossOrigin(origins = "*")
public class SurveyController {

    @Autowired
    private SurveyService surveyService;

    @GetMapping
    public ResponseEntity<List<SurveyDTO>> getAllSurveys() {
        List<SurveyDTO> surveys = surveyService.getAllSurveys();
        return ResponseEntity.ok(surveys);
    }

    @GetMapping("/status/{status}")
    public ResponseEntity<List<SurveyDTO>> getSurveysByStatus(@PathVariable Integer status) {
        List<SurveyDTO> surveys = surveyService.getSurveysByStatus(status);
        return ResponseEntity.ok(surveys);
    }

    @GetMapping("/{id}")
    public ResponseEntity<SurveyDTO> getSurveyById(@PathVariable Integer id) {
        SurveyDTO survey = surveyService.getSurveyById(id);
        if (survey != null) {
            return ResponseEntity.ok(survey);
        }
        return ResponseEntity.notFound().build();
    }

    @PostMapping
    public ResponseEntity<SurveyDTO> createSurvey(@Valid @RequestBody SurveyDTO surveyDTO) {
        SurveyDTO createdSurvey = surveyService.createSurvey(surveyDTO);
        return ResponseEntity.ok(createdSurvey);
    }

    @PutMapping("/{id}")
    public ResponseEntity<SurveyDTO> updateSurvey(@PathVariable Integer id, @Valid @RequestBody SurveyDTO surveyDTO) {
        SurveyDTO updatedSurvey = surveyService.updateSurvey(id, surveyDTO);
        if (updatedSurvey != null) {
            return ResponseEntity.ok(updatedSurvey);
        }
        return ResponseEntity.notFound().build();
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteSurvey(@PathVariable Integer id) {
        boolean deleted = surveyService.deleteSurvey(id);
        if (deleted) {
            return ResponseEntity.ok().build();
        }
        return ResponseEntity.notFound().build();
    }

    @GetMapping("/search")
    public ResponseEntity<List<SurveyDTO>> searchSurveys(@RequestParam String keyword) {
        List<SurveyDTO> surveys = surveyService.searchSurveys(keyword);
        return ResponseEntity.ok(surveys);
    }
}
