package com.demo.survey.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AnswerDTO {

    private Integer id;

    private Integer surveyId;

    private Integer userId;

    private Integer questionId;

    private String answer;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String questionContent; // 问题内容

    private String surveyName; // 问卷名称
}
