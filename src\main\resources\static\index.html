<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调查问卷系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h2 {
            color: #555;
            margin-top: 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>调查问卷系统 Demo</h1>
        
        <div class="section">
            <h2>问卷管理</h2>
            <button onclick="getAllSurveys()">获取所有问卷</button>
            <button onclick="getActiveSurveys()">获取启用的问卷</button>
            <br>
            <input type="text" id="surveyName" placeholder="问卷名称">
            <button onclick="createSurvey()">创建问卷</button>
            <div id="surveyResult" class="result"></div>
        </div>

        <div class="section">
            <h2>问题配置</h2>
            <input type="number" id="configSurveyId" placeholder="问卷ID">
            <input type="text" id="questionContent" placeholder="问题内容">
            <button onclick="createQuestion()">添加问题</button>
            <br>
            <input type="text" id="optionContent" placeholder="选项内容">
            <button onclick="createOption()">添加选项</button>
            <div id="configResult" class="result"></div>
        </div>

        <div class="section">
            <h2>答案提交</h2>
            <input type="number" id="answerSurveyId" placeholder="问卷ID">
            <input type="number" id="answerUserId" placeholder="用户ID">
            <input type="number" id="answerQuestionId" placeholder="问题ID">
            <textarea id="answerContent" placeholder="答案内容"></textarea>
            <button onclick="submitAnswer()">提交答案</button>
            <div id="answerResult" class="result"></div>
        </div>

        <div class="section">
            <h2>统计查询</h2>
            <input type="number" id="statsSurveyId" placeholder="问卷ID">
            <button onclick="getAnswers()">查看答案</button>
            <button onclick="getParticipantCount()">参与人数统计</button>
            <div id="statsResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = '/api';

        async function apiCall(url, method = 'GET', data = null) {
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };
            if (data) {
                options.body = JSON.stringify(data);
            }
            
            try {
                const response = await fetch(API_BASE + url, options);
                const result = await response.json();
                return result;
            } catch (error) {
                return { error: error.message };
            }
        }

        async function getAllSurveys() {
            const result = await apiCall('/surveys');
            document.getElementById('surveyResult').textContent = JSON.stringify(result, null, 2);
        }

        async function getActiveSurveys() {
            const result = await apiCall('/surveys/status/1');
            document.getElementById('surveyResult').textContent = JSON.stringify(result, null, 2);
        }

        async function createSurvey() {
            const surveyName = document.getElementById('surveyName').value;
            if (!surveyName) {
                alert('请输入问卷名称');
                return;
            }
            
            const data = {
                surveyName: surveyName,
                status: 1
            };
            
            const result = await apiCall('/surveys', 'POST', data);
            document.getElementById('surveyResult').textContent = JSON.stringify(result, null, 2);
        }

        async function createQuestion() {
            const surveyId = document.getElementById('configSurveyId').value;
            const content = document.getElementById('questionContent').value;
            
            if (!surveyId || !content) {
                alert('请输入问卷ID和问题内容');
                return;
            }
            
            const data = {
                surveyId: parseInt(surveyId),
                type: 1,
                content: content
            };
            
            const result = await apiCall('/survey-configs', 'POST', data);
            document.getElementById('configResult').textContent = JSON.stringify(result, null, 2);
        }

        async function createOption() {
            const surveyId = document.getElementById('configSurveyId').value;
            const content = document.getElementById('optionContent').value;
            
            if (!surveyId || !content) {
                alert('请输入问卷ID和选项内容');
                return;
            }
            
            const data = {
                surveyId: parseInt(surveyId),
                type: 2,
                content: content
            };
            
            const result = await apiCall('/survey-configs', 'POST', data);
            document.getElementById('configResult').textContent = JSON.stringify(result, null, 2);
        }

        async function submitAnswer() {
            const surveyId = document.getElementById('answerSurveyId').value;
            const userId = document.getElementById('answerUserId').value;
            const questionId = document.getElementById('answerQuestionId').value;
            const answer = document.getElementById('answerContent').value;
            
            if (!surveyId || !userId || !questionId || !answer) {
                alert('请填写所有字段');
                return;
            }
            
            const data = {
                surveyId: parseInt(surveyId),
                userId: parseInt(userId),
                questionId: parseInt(questionId),
                answer: answer
            };
            
            const result = await apiCall('/answers', 'POST', data);
            document.getElementById('answerResult').textContent = JSON.stringify(result, null, 2);
        }

        async function getAnswers() {
            const surveyId = document.getElementById('statsSurveyId').value;
            if (!surveyId) {
                alert('请输入问卷ID');
                return;
            }
            
            const result = await apiCall(`/answers/survey/${surveyId}`);
            document.getElementById('statsResult').textContent = JSON.stringify(result, null, 2);
        }

        async function getParticipantCount() {
            const surveyId = document.getElementById('statsSurveyId').value;
            if (!surveyId) {
                alert('请输入问卷ID');
                return;
            }
            
            const result = await apiCall(`/answers/survey/${surveyId}/participants/count`);
            document.getElementById('statsResult').textContent = `参与人数: ${result}`;
        }
    </script>
</body>
</html>
