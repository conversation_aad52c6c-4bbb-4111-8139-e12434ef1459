package com.demo.survey.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "survey_config")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SurveyConfig {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "survey_id")
    private Integer surveyId;

    @Column(name = "config_type")
    private Integer configType; // 1问题,2选项

    @Column(name = "option_type")
    private Integer optionType; // 问题类型:1填空,2单选,3多选

    @Column(name = "question_id")
    private Integer questionId; // 选项所属的问题ID（仅当configType=2时使用）

    @Size(max = 255, message = "内容长度不能超过255个字符")
    @Column(name = "content", length = 255)
    private String content;

    @Column(name = "create_time")
    private LocalDateTime createTime;

    @Column(name = "update_time")
    private LocalDateTime updateTime;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "survey_id", insertable = false, updatable = false)
    private Survey survey;

    @OneToMany(mappedBy = "question", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Answer> answers;

    @PrePersist
    protected void onCreate() {
        createTime = LocalDateTime.now();
        updateTime = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updateTime = LocalDateTime.now();
    }

    // 配置类型常量
    public static final int CONFIG_TYPE_QUESTION = 1;
    public static final int CONFIG_TYPE_OPTION = 2;

    // 问题类型常量
    public static final int OPTION_TYPE_TEXT = 1;      // 填空
    public static final int OPTION_TYPE_SINGLE = 2;    // 单选
    public static final int OPTION_TYPE_MULTIPLE = 3;  // 多选
}
