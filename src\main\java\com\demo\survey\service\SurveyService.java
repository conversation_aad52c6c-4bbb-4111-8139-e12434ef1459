package com.demo.survey.service;

import com.demo.survey.dto.SurveyDTO;
import com.demo.survey.entity.Survey;
import com.demo.survey.entity.SurveyConfig;
import com.demo.survey.repository.AnswerRepository;
import com.demo.survey.repository.SurveyConfigRepository;
import com.demo.survey.repository.SurveyRepository;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional
public class SurveyService {

    @Autowired
    private SurveyRepository surveyRepository;

    @Autowired
    private SurveyConfigRepository surveyConfigRepository;

    @Autowired
    private AnswerRepository answerRepository;

    public List<SurveyDTO> getAllSurveys() {
        List<Survey> surveys = surveyRepository.findAll();
        return surveys.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    public List<SurveyDTO> getSurveysByStatus(Integer status) {
        List<Survey> surveys = surveyRepository.findByStatusOrderByCreateTimeDesc(status);
        return surveys.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    public SurveyDTO getSurveyById(Integer id) {
        Optional<Survey> survey = surveyRepository.findById(id);
        return survey.map(this::convertToDTO).orElse(null);
    }

    public SurveyDTO createSurvey(SurveyDTO surveyDTO) {
        Survey survey = new Survey();
        BeanUtils.copyProperties(surveyDTO, survey);
        Survey savedSurvey = surveyRepository.save(survey);
        return convertToDTO(savedSurvey);
    }

    public SurveyDTO updateSurvey(Integer id, SurveyDTO surveyDTO) {
        Optional<Survey> existingSurvey = surveyRepository.findById(id);
        if (existingSurvey.isPresent()) {
            Survey survey = existingSurvey.get();
            survey.setSurveyName(surveyDTO.getSurveyName());
            survey.setStatus(surveyDTO.getStatus());
            Survey savedSurvey = surveyRepository.save(survey);
            return convertToDTO(savedSurvey);
        }
        return null;
    }

    public boolean deleteSurvey(Integer id) {
        if (surveyRepository.existsById(id)) {
            surveyRepository.deleteById(id);
            return true;
        }
        return false;
    }

    public List<SurveyDTO> searchSurveys(String keyword) {
        List<Survey> surveys = surveyRepository.findByKeyword(keyword);
        return surveys.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    private SurveyDTO convertToDTO(Survey survey) {
        SurveyDTO dto = new SurveyDTO();
        BeanUtils.copyProperties(survey, dto);
        
        // 获取参与人数
        Long participantCount = answerRepository.countDistinctUsersBySurveyId(survey.getId());
        dto.setParticipantCount(participantCount);
        
        return dto;
    }
}
