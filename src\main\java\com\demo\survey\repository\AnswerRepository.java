package com.demo.survey.repository;

import com.demo.survey.entity.Answer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AnswerRepository extends JpaRepository<Answer, Integer> {

    List<Answer> findBySurveyIdOrderByCreateTimeDesc(Integer surveyId);

    List<Answer> findByUserIdOrderByCreateTimeDesc(Integer userId);

    List<Answer> findBySurveyIdAndUserIdOrderByCreateTimeDesc(Integer surveyId, Integer userId);

    @Query("SELECT COUNT(DISTINCT a.userId) FROM Answer a WHERE a.surveyId = :surveyId")
    Long countDistinctUsersBySurveyId(@Param("surveyId") Integer surveyId);

    List<Answer> findByQuestionIdOrderByCreateTimeDesc(Integer questionId);
}
