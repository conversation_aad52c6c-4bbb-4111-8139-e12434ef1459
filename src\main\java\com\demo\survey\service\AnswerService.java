package com.demo.survey.service;

import com.demo.survey.dto.AnswerDTO;
import com.demo.survey.entity.Answer;
import com.demo.survey.repository.AnswerRepository;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional
public class AnswerService {

    @Autowired
    private AnswerRepository answerRepository;

    public List<AnswerDTO> getAnswersBySurveyId(Integer surveyId) {
        List<Answer> answers = answerRepository.findBySurveyIdOrderByCreateTimeDesc(surveyId);
        return answers.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    public List<AnswerDTO> getAnswersByUserId(Integer userId) {
        List<Answer> answers = answerRepository.findByUserIdOrderByCreateTimeDesc(userId);
        return answers.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    public List<AnswerDTO> getAnswersBySurveyIdAndUserId(Integer surveyId, Integer userId) {
        List<Answer> answers = answerRepository.findBySurveyIdAndUserIdOrderByCreateTimeDesc(surveyId, userId);
        return answers.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    public AnswerDTO createAnswer(AnswerDTO answerDTO) {
        Answer answer = new Answer();
        BeanUtils.copyProperties(answerDTO, answer);
        Answer savedAnswer = answerRepository.save(answer);
        return convertToDTO(savedAnswer);
    }

    public AnswerDTO updateAnswer(Integer id, AnswerDTO answerDTO) {
        Optional<Answer> existingAnswer = answerRepository.findById(id);
        if (existingAnswer.isPresent()) {
            Answer answer = existingAnswer.get();
            answer.setAnswer(answerDTO.getAnswer());
            Answer savedAnswer = answerRepository.save(answer);
            return convertToDTO(savedAnswer);
        }
        return null;
    }

    public boolean deleteAnswer(Integer id) {
        if (answerRepository.existsById(id)) {
            answerRepository.deleteById(id);
            return true;
        }
        return false;
    }

    public Long getParticipantCount(Integer surveyId) {
        return answerRepository.countDistinctUsersBySurveyId(surveyId);
    }

    private AnswerDTO convertToDTO(Answer answer) {
        AnswerDTO dto = new AnswerDTO();
        BeanUtils.copyProperties(answer, dto);
        return dto;
    }
}
