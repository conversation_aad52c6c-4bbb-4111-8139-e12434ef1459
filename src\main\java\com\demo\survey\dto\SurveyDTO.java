package com.demo.survey.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SurveyDTO {

    private Integer id;

    @NotBlank(message = "问卷名不能为空")
    @Size(max = 255, message = "问卷名长度不能超过255个字符")
    private String surveyName;

    private Integer status; // 1启用,2停用

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private List<SurveyConfigDTO> questions;

    private List<SurveyConfigDTO> options;

    private Long participantCount; // 参与人数
}
