<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调查问卷系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header {
            background: #343a40;
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 1.8rem;
            margin-bottom: 5px;
        }

        .main-content {
            padding: 30px;
        }

        .action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .action-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 20px;
            text-align: center;
        }

        .action-card h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .action-card p {
            color: #666;
            margin-bottom: 15px;
            font-size: 0.9rem;
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            background: #0056b3;
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            position: absolute;
            right: 15px;
            top: 10px;
        }

        .close:hover {
            color: black;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }

        .alert {
            padding: 12px;
            margin-bottom: 15px;
            border-radius: 4px;
        }

        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .alert-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .result-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            max-height: 300px;
            overflow-y: auto;
        }

        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .btn-group .btn {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>调查问卷管理系统</h1>
            <p>简洁高效的问卷管理平台</p>
        </div>

        <div class="main-content">
            <div class="action-grid">
                <div class="action-card">
                    <h3>问卷管理</h3>
                    <p>创建和管理调查问卷</p>
                    <button class="btn" onclick="openModal('surveyModal')">管理问卷</button>
                </div>

                <div class="action-card">
                    <h3>问题配置</h3>
                    <p>添加问题和选项</p>
                    <button class="btn" onclick="openModal('questionModal')">配置问题</button>
                </div>

                <div class="action-card">
                    <h3>填写问卷</h3>
                    <p>参与问卷调查</p>
                    <button class="btn" onclick="openModal('fillModal')">填写问卷</button>
                </div>

                <div class="action-card">
                    <h3>数据统计</h3>
                    <p>查看问卷统计结果</p>
                    <button class="btn" onclick="openModal('statsModal')">查看统计</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 问卷管理模态框 -->
    <div id="surveyModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('surveyModal')">&times;</span>
            <h2>问卷管理</h2>
            
            <div class="form-group">
                <label for="surveyTitle">问卷标题</label>
                <input type="text" id="surveyTitle" class="form-control" placeholder="请输入问卷标题">
            </div>
            
            <div class="form-group">
                <label for="surveyDescription">问卷描述</label>
                <textarea id="surveyDescription" class="form-control" rows="3" placeholder="请输入问卷描述"></textarea>
            </div>
            
            <div class="btn-group">
                <button class="btn" onclick="createSurvey()">创建问卷</button>
                <button class="btn btn-secondary" onclick="loadSurveys()">查看问卷</button>
            </div>
            
            <div id="surveyResult" class="result-area"></div>
        </div>
    </div>

    <!-- 问题配置模态框 -->
    <div id="questionModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('questionModal')">&times;</span>
            <h2>问题配置</h2>
            
            <div class="form-group">
                <label for="questionSurveyId">选择问卷</label>
                <select id="questionSurveyId" class="form-control">
                    <option value="">请选择问卷</option>
                </select>
            </div>
            
            <div style="border-top: 1px solid #dee2e6; margin: 20px 0; padding-top: 20px;">
                <h4>添加问题</h4>
                <div class="form-group">
                    <label for="questionType">问题类型</label>
                    <select id="questionType" class="form-control">
                        <option value="1">填空题</option>
                        <option value="2">单选题</option>
                        <option value="3">多选题</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="questionContent">问题内容</label>
                    <input type="text" id="questionContent" class="form-control" placeholder="请输入问题内容">
                </div>
                
                <button class="btn" onclick="addQuestion()">添加问题</button>
            </div>
            
            <div style="border-top: 1px solid #dee2e6; margin: 20px 0; padding-top: 20px;">
                <h4>添加选项</h4>
                <div class="form-group">
                    <label for="optionQuestionId">选择问题</label>
                    <select id="optionQuestionId" class="form-control">
                        <option value="">请先选择问卷并添加问题</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="optionContent">选项内容</label>
                    <input type="text" id="optionContent" class="form-control" placeholder="请输入选项内容">
                </div>
                
                <button class="btn btn-secondary" onclick="addOption()">添加选项</button>
            </div>
            
            <div id="questionResult" class="result-area"></div>
        </div>
    </div>

    <!-- 填写问卷模态框 -->
    <div id="fillModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('fillModal')">&times;</span>
            <h2>填写问卷</h2>

            <div class="form-group">
                <label for="fillSurveyId">选择问卷</label>
                <select id="fillSurveyId" class="form-control" onchange="loadSurveyQuestions()">
                    <option value="">请选择问卷</option>
                </select>
            </div>

            <div class="form-group">
                <label for="userId">用户ID</label>
                <input type="number" id="userId" class="form-control" placeholder="请输入用户ID">
            </div>

            <div id="surveyQuestions"></div>

            <button class="btn" onclick="submitAllAnswers()" style="margin-top: 15px;">提交答案</button>

            <div id="fillResult" class="result-area"></div>
        </div>
    </div>

    <!-- 数据统计模态框 -->
    <div id="statsModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('statsModal')">&times;</span>
            <h2>数据统计</h2>

            <div class="form-group">
                <label for="statsSurveyId">选择问卷</label>
                <select id="statsSurveyId" class="form-control">
                    <option value="">请选择问卷</option>
                </select>
            </div>

            <div class="btn-group">
                <button class="btn" onclick="getAnswers()">查看答案</button>
                <button class="btn btn-secondary" onclick="getParticipantCount()">参与统计</button>
            </div>

            <div id="statsResult" class="result-area"></div>
        </div>
    </div>

    <script>
        const API_BASE = '/api';

        // 通用API调用函数
        async function apiCall(endpoint, method = 'GET', data = null) {
            const config = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };
            
            if (data) {
                config.body = JSON.stringify(data);
            }
            
            const response = await fetch(API_BASE + endpoint, config);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        }

        // 模态框管理
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
            
            // 加载问卷列表到下拉框
            if (modalId === 'questionModal' || modalId === 'fillModal' || modalId === 'statsModal') {
                loadSurveyOptions();
            }
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }

        // 加载问卷选项到下拉框
        async function loadSurveyOptions() {
            try {
                const surveys = await apiCall('/surveys');
                const selects = ['questionSurveyId', 'fillSurveyId', 'statsSurveyId'];

                selects.forEach(selectId => {
                    const selectElement = document.getElementById(selectId);
                    if (selectElement) {
                        selectElement.innerHTML = '<option value="">请选择问卷</option>' +
                            surveys.map(s => `<option value="${s.id}">${s.title}</option>`).join('');
                    }
                });
            } catch (error) {
                console.error('加载问卷失败:', error);
            }
        }

        // 创建问卷
        async function createSurvey() {
            const title = document.getElementById('surveyTitle').value;
            const description = document.getElementById('surveyDescription').value;

            if (!title.trim()) {
                alert('请输入问卷标题');
                return;
            }

            try {
                const data = { title: title.trim(), description: description.trim() };
                const result = await apiCall('/surveys', 'POST', data);

                document.getElementById('surveyResult').innerHTML =
                    '<div class="alert alert-success">问卷创建成功！ID: ' + result.id + '</div>';

                // 清空表单
                document.getElementById('surveyTitle').value = '';
                document.getElementById('surveyDescription').value = '';

                // 刷新问卷列表
                loadSurveyOptions();
            } catch (error) {
                document.getElementById('surveyResult').innerHTML =
                    '<div class="alert alert-error">创建失败: ' + error.message + '</div>';
            }
        }

        // 查看问卷列表
        async function loadSurveys() {
            try {
                const surveys = await apiCall('/surveys');
                const resultDiv = document.getElementById('surveyResult');

                if (surveys && surveys.length > 0) {
                    let html = '<h4>问卷列表：</h4>';
                    surveys.forEach(survey => {
                        html += `<div style="padding: 10px; border: 1px solid #dee2e6; margin: 5px 0; border-radius: 4px;">
                            <strong>${survey.title}</strong><br>
                            <small style="color: #666;">${survey.description || '无描述'}</small><br>
                            <small>创建时间: ${new Date(survey.createTime).toLocaleString()}</small>
                        </div>`;
                    });
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = '<div class="alert alert-info">暂无问卷</div>';
                }
            } catch (error) {
                document.getElementById('surveyResult').innerHTML =
                    '<div class="alert alert-error">加载失败: ' + error.message + '</div>';
            }
        }

        // 添加问题
        async function addQuestion() {
            const surveyId = document.getElementById('questionSurveyId').value;
            const questionType = document.getElementById('questionType').value;
            const content = document.getElementById('questionContent').value;

            if (!surveyId) {
                alert('请选择问卷');
                return;
            }

            if (!content.trim()) {
                alert('请输入问题内容');
                return;
            }

            try {
                const data = {
                    surveyId: parseInt(surveyId),
                    configType: 1,
                    optionType: parseInt(questionType),
                    content: content.trim()
                };

                const result = await apiCall('/survey-configs', 'POST', data);

                if (result.id) {
                    alert('问题添加成功！');
                    document.getElementById('questionContent').value = '';
                    loadQuestionsList(surveyId);
                    loadQuestionOptions(surveyId);
                } else {
                    alert('添加失败');
                }
            } catch (error) {
                alert('添加失败: ' + error.message);
            }
        }

        // 添加选项
        async function addOption() {
            const surveyId = document.getElementById('questionSurveyId').value;
            const questionId = document.getElementById('optionQuestionId').value;
            const content = document.getElementById('optionContent').value;

            if (!surveyId) {
                alert('请选择问卷');
                return;
            }

            if (!questionId) {
                alert('请选择问题');
                return;
            }

            if (!content.trim()) {
                alert('请输入选项内容');
                return;
            }

            try {
                const data = {
                    surveyId: parseInt(surveyId),
                    configType: 2,
                    questionId: parseInt(questionId),
                    optionType: null,
                    content: content.trim()
                };

                const result = await apiCall('/survey-configs', 'POST', data);

                if (result.id) {
                    alert('选项添加成功！');
                    document.getElementById('optionContent').value = '';
                    loadQuestionsList(surveyId);
                } else {
                    alert('添加失败');
                }
            } catch (error) {
                alert('添加失败: ' + error.message);
            }
        }

        // 加载问题选项到下拉框
        async function loadQuestionOptions(surveyId) {
            if (!surveyId) {
                document.getElementById('optionQuestionId').innerHTML = '<option value="">请先选择问卷并添加问题</option>';
                return;
            }

            try {
                const questions = await apiCall(`/survey-configs/survey/${surveyId}/questions`);
                const selectElement = document.getElementById('optionQuestionId');

                if (questions && questions.length > 0) {
                    selectElement.innerHTML = '<option value="">请选择问题</option>' +
                        questions.map(q => `<option value="${q.id}">${q.content}</option>`).join('');
                } else {
                    selectElement.innerHTML = '<option value="">暂无问题，请先添加问题</option>';
                }
            } catch (error) {
                document.getElementById('optionQuestionId').innerHTML = '<option value="">加载问题失败</option>';
            }
        }

        // 加载问题列表
        async function loadQuestionsList(surveyId) {
            if (!surveyId) {
                document.getElementById('questionResult').innerHTML = '';
                return;
            }

            try {
                const questions = await apiCall(`/survey-configs/survey/${surveyId}/questions`);
                const resultDiv = document.getElementById('questionResult');
                let html = '<h4>当前问题和选项：</h4>';

                if (questions && questions.length > 0) {
                    for (const question of questions) {
                        const typeText = question.optionType === 1 ? '填空题' : question.optionType === 2 ? '单选题' : '多选题';
                        html += `<div style="padding: 10px; background: #f8f9fa; margin: 10px 0; border-radius: 4px;">
                            <strong>${question.content}</strong> <span style="color: #666;">(${typeText})</span>`;

                        // 如果是选择题，加载其选项
                        if (question.optionType === 2 || question.optionType === 3) {
                            try {
                                const options = await apiCall(`/survey-configs/question/${question.id}/options`);
                                if (options && options.length > 0) {
                                    html += '<div style="margin-left: 15px; margin-top: 5px;">';
                                    options.forEach(option => {
                                        html += `<div style="padding: 3px 0; font-size: 0.9em;">• ${option.content}</div>`;
                                    });
                                    html += '</div>';
                                } else {
                                    html += '<div style="margin-left: 15px; color: #999; font-style: italic;">暂无选项</div>';
                                }
                            } catch (error) {
                                html += '<div style="margin-left: 15px; color: #dc3545;">选项加载失败</div>';
                            }
                        }

                        html += '</div>';
                    }
                } else {
                    html += '<div class="alert alert-info">暂无问题</div>';
                }

                resultDiv.innerHTML = html;
            } catch (error) {
                document.getElementById('questionResult').innerHTML =
                    '<div class="alert alert-error">加载失败: ' + error.message + '</div>';
            }
        }

        // 加载问卷问题用于填写
        async function loadSurveyQuestions() {
            const surveyId = document.getElementById('fillSurveyId').value;
            const questionsDiv = document.getElementById('surveyQuestions');

            if (!surveyId) {
                questionsDiv.innerHTML = '';
                return;
            }

            try {
                const questions = await apiCall(`/survey-configs/survey/${surveyId}/questions`);

                if (!questions || questions.length === 0) {
                    questionsDiv.innerHTML = '<div class="alert alert-info">该问卷暂无问题</div>';
                    return;
                }

                let html = '<h4>问卷问题：</h4>';

                for (let i = 0; i < questions.length; i++) {
                    const question = questions[i];
                    html += `<div class="form-group" style="border: 1px solid #dee2e6; padding: 15px; border-radius: 4px; margin-bottom: 15px;">
                        <label><strong>问题 ${i + 1}: ${question.content}</strong></label>
                        <input type="hidden" class="question-id" value="${question.id}">`;

                    if (question.optionType === 1) { // 填空题
                        html += `<input type="text" class="form-control answer-input" placeholder="请输入答案" style="margin-top: 10px;">`;
                    } else if (question.optionType === 2 || question.optionType === 3) { // 单选题或多选题
                        try {
                            const options = await apiCall(`/survey-configs/question/${question.id}/options`);
                            html += '<div style="margin-top: 10px;">';

                            if (options && options.length > 0) {
                                const inputType = question.optionType === 2 ? 'radio' : 'checkbox';
                                options.forEach(option => {
                                    html += `<label style="display: block; margin: 5px 0;">
                                        <input type="${inputType}" name="question_${question.id}" value="${option.content}" style="margin-right: 8px;">
                                        ${option.content}
                                    </label>`;
                                });
                            } else {
                                html += '<div style="color: #999; font-style: italic;">该问题暂无选项</div>';
                            }
                            html += '</div>';
                        } catch (error) {
                            html += '<div style="color: #dc3545; margin-top: 10px;">选项加载失败</div>';
                        }
                    }
                    html += '</div>';
                }

                questionsDiv.innerHTML = html;
            } catch (error) {
                questionsDiv.innerHTML = '<div class="alert alert-error">加载问题失败: ' + error.message + '</div>';
            }
        }

        // 提交所有答案
        async function submitAllAnswers() {
            const surveyId = document.getElementById('fillSurveyId').value;
            const userId = document.getElementById('userId').value;

            if (!surveyId) {
                alert('请选择问卷');
                return;
            }

            if (!userId) {
                alert('请输入用户ID');
                return;
            }

            const questionDivs = document.querySelectorAll('#surveyQuestions .form-group');
            const answers = [];

            questionDivs.forEach(div => {
                const questionId = div.querySelector('.question-id').value;
                const textInput = div.querySelector('.answer-input');
                const radioInputs = div.querySelectorAll('input[type="radio"]:checked');
                const checkboxInputs = div.querySelectorAll('input[type="checkbox"]:checked');

                let answer = '';

                if (textInput) {
                    answer = textInput.value.trim();
                } else if (radioInputs.length > 0) {
                    answer = radioInputs[0].value;
                } else if (checkboxInputs.length > 0) {
                    answer = Array.from(checkboxInputs).map(cb => cb.value).join(', ');
                }

                if (answer) {
                    answers.push({
                        surveyId: parseInt(surveyId),
                        userId: parseInt(userId),
                        questionId: parseInt(questionId),
                        answer: answer
                    });
                }
            });

            if (answers.length === 0) {
                alert('请至少回答一个问题');
                return;
            }

            try {
                const results = [];
                for (const answerData of answers) {
                    const result = await apiCall('/answers', 'POST', answerData);
                    results.push(result);
                }

                document.getElementById('fillResult').innerHTML =
                    '<div class="alert alert-success">成功提交 ' + results.length + ' 个答案！</div>';

                // 清空表单
                document.getElementById('userId').value = '';
                document.getElementById('surveyQuestions').innerHTML = '';
                document.getElementById('fillSurveyId').value = '';

            } catch (error) {
                document.getElementById('fillResult').innerHTML =
                    '<div class="alert alert-error">提交失败: ' + error.message + '</div>';
            }
        }

        // 获取问卷答案
        async function getAnswers() {
            const surveyId = document.getElementById('statsSurveyId').value;
            if (!surveyId) {
                alert('请选择问卷');
                return;
            }

            try {
                const result = await apiCall(`/answers/survey/${surveyId}`);
                const statsDiv = document.getElementById('statsResult');

                if (result && result.length > 0) {
                    let html = '<h4>答案列表：</h4>';
                    html += '<div style="max-height: 300px; overflow-y: auto;">';

                    result.forEach((answer, index) => {
                        html += `<div style="border: 1px solid #dee2e6; padding: 10px; margin: 5px 0; border-radius: 4px; background: #f8f9fa;">
                            <div><strong>答案 ${index + 1}</strong></div>
                            <div>用户ID: ${answer.userId}</div>
                            <div>问题ID: ${answer.questionId}</div>
                            <div>答案: ${answer.answer}</div>
                            <div style="color: #666; font-size: 0.9em;">提交时间: ${new Date(answer.createTime).toLocaleString()}</div>
                        </div>`;
                    });

                    html += '</div>';
                    statsDiv.innerHTML = html;
                } else {
                    statsDiv.innerHTML = '<div class="alert alert-info">暂无答案数据</div>';
                }
            } catch (error) {
                document.getElementById('statsResult').innerHTML =
                    '<div class="alert alert-error">获取答案失败: ' + error.message + '</div>';
            }
        }

        // 获取参与人数统计
        async function getParticipantCount() {
            const surveyId = document.getElementById('statsSurveyId').value;
            if (!surveyId) {
                alert('请选择问卷');
                return;
            }

            try {
                const result = await apiCall(`/answers/survey/${surveyId}/participants/count`);
                const statsDiv = document.getElementById('statsResult');

                statsDiv.innerHTML = `
                    <div class="alert alert-success">
                        <h4>📊 参与统计</h4>
                        <div style="font-size: 1.2em; margin-top: 10px;">
                            <strong>参与人数: ${result.count || 0} 人</strong>
                        </div>
                    </div>
                `;
            } catch (error) {
                document.getElementById('statsResult').innerHTML =
                    '<div class="alert alert-error">获取统计失败: ' + error.message + '</div>';
            }
        }

        // 监听问卷选择变化
        document.addEventListener('change', function(e) {
            if (e.target.id === 'questionSurveyId') {
                const surveyId = e.target.value;
                loadQuestionsList(surveyId);
                loadQuestionOptions(surveyId);
            }
        });
    </script>
</body>
</html>
