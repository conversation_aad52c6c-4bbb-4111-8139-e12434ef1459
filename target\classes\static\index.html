<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调查问卷系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .action-card {
            background: white;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 2px solid #f0f0f0;
        }

        .action-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .action-card .icon {
            font-size: 3em;
            margin-bottom: 20px;
            color: #667eea;
        }

        .action-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.5em;
        }

        .action-card p {
            color: #666;
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 30px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 1.5em;
        }

        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s ease;
        }

        .close:hover {
            opacity: 0.7;
        }

        .modal-body {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-control select {
            cursor: pointer;
        }

        .modal-footer {
            padding: 20px 30px;
            border-top: 1px solid #e0e0e0;
            display: flex;
            justify-content: flex-end;
            gap: 15px;
        }

        .survey-list {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .survey-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
            transition: transform 0.2s ease;
        }

        .survey-item:hover {
            transform: translateX(5px);
        }

        .survey-item h4 {
            color: #333;
            margin-bottom: 10px;
        }

        .survey-item .meta {
            color: #666;
            font-size: 0.9em;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-active {
            background-color: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            border: 1px solid transparent;
        }

        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }

        .alert-error {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 调查问卷系统</h1>
            <p>创建、管理和分析您的调查问卷</p>
        </div>

        <div class="main-content">
            <div class="action-grid">
                <div class="action-card">
                    <div class="icon">📝</div>
                    <h3>创建问卷</h3>
                    <p>快速创建新的调查问卷，设置问卷名称和基本信息</p>
                    <button class="btn" onclick="openCreateSurveyModal()">创建问卷</button>
                </div>

                <div class="action-card">
                    <div class="icon">❓</div>
                    <h3>管理问题</h3>
                    <p>为问卷添加各种类型的问题和选项</p>
                    <button class="btn btn-secondary" onclick="openManageQuestionsModal()">管理问题</button>
                </div>

                <div class="action-card">
                    <div class="icon">✍️</div>
                    <h3>填写问卷</h3>
                    <p>模拟用户填写问卷的过程</p>
                    <button class="btn btn-success" onclick="openFillSurveyModal()">填写问卷</button>
                </div>

                <div class="action-card">
                    <div class="icon">📊</div>
                    <h3>查看统计</h3>
                    <p>查看问卷的回答统计和参与人数</p>
                    <button class="btn" onclick="openStatsModal()">查看统计</button>
                </div>
            </div>

            <div class="survey-list">
                <h2>📋 问卷列表</h2>
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    正在加载问卷列表...
                </div>
                <div id="surveyList"></div>
                <button class="btn" onclick="loadSurveys()" style="margin-top: 20px;">刷新列表</button>
            </div>
        </div>
    </div>

    <!-- 创建问卷弹窗 -->
    <div id="createSurveyModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>📝 创建新问卷</h2>
                <span class="close" onclick="closeModal('createSurveyModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="newSurveyName">问卷名称</label>
                    <input type="text" id="newSurveyName" class="form-control" placeholder="请输入问卷名称">
                </div>
                <div class="form-group">
                    <label for="surveyStatus">状态</label>
                    <select id="surveyStatus" class="form-control">
                        <option value="1">启用</option>
                        <option value="2">停用</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="closeModal('createSurveyModal')">取消</button>
                <button class="btn" onclick="createSurvey()">创建问卷</button>
            </div>
        </div>
    </div>

    <!-- 管理问题弹窗 -->
    <div id="manageQuestionsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>❓ 管理问题</h2>
                <span class="close" onclick="closeModal('manageQuestionsModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="questionSurveyId">选择问卷</label>
                    <select id="questionSurveyId" class="form-control">
                        <option value="">请选择问卷</option>
                    </select>
                </div>

                <div style="border-top: 1px solid #e0e0e0; margin: 20px 0; padding-top: 20px;">
                    <h4>添加问题</h4>
                    <div class="form-group">
                        <label for="questionContent">问题内容</label>
                        <textarea id="questionContent" class="form-control" rows="3" placeholder="请输入问题内容"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="questionType">问题类型</label>
                        <select id="questionType" class="form-control">
                            <option value="1">填空题</option>
                            <option value="2">单选题</option>
                            <option value="3">多选题</option>
                        </select>
                    </div>
                    <button class="btn" onclick="addQuestion()">添加问题</button>
                </div>

                <div style="border-top: 1px solid #e0e0e0; margin: 20px 0; padding-top: 20px;">
                    <h4>添加选项</h4>
                    <div class="form-group">
                        <label for="optionQuestionId">选择问题</label>
                        <select id="optionQuestionId" class="form-control">
                            <option value="">请先选择问卷并添加问题</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="optionContent">选项内容</label>
                        <input type="text" id="optionContent" class="form-control" placeholder="请输入选项内容">
                    </div>
                    <button class="btn btn-secondary" onclick="addOption()">添加选项</button>
                </div>

                <div id="questionResult"></div>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="closeModal('manageQuestionsModal')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 填写问卷弹窗 -->
    <div id="fillSurveyModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>✍️ 填写问卷</h2>
                <span class="close" onclick="closeModal('fillSurveyModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="fillSurveyId">选择问卷</label>
                    <select id="fillSurveyId" class="form-control" onchange="loadSurveyQuestions()">
                        <option value="">请选择问卷</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="userId">用户ID</label>
                    <input type="number" id="userId" class="form-control" placeholder="请输入用户ID">
                </div>

                <div id="surveyQuestions"></div>

                <div id="fillResult"></div>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="closeModal('fillSurveyModal')">取消</button>
                <button class="btn btn-success" onclick="submitAllAnswers()">提交答案</button>
            </div>
        </div>
    </div>

    <!-- 统计查看弹窗 -->
    <div id="statsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>📊 问卷统计</h2>
                <span class="close" onclick="closeModal('statsModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="statsSurveyId">选择问卷</label>
                    <select id="statsSurveyId" class="form-control">
                        <option value="">请选择问卷</option>
                    </select>
                </div>
                <div style="margin: 20px 0;">
                    <button class="btn" onclick="getParticipantCount()">参与人数统计</button>
                    <button class="btn btn-secondary" onclick="getAnswers()">查看所有答案</button>
                </div>
                <div id="statsResult"></div>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="closeModal('statsModal')">关闭</button>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '/api';
        let surveys = [];

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadSurveys();
        });

        async function apiCall(url, method = 'GET', data = null) {
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };
            if (data) {
                options.body = JSON.stringify(data);
            }

            try {
                const response = await fetch(API_BASE + url, options);
                const result = await response.json();
                return result;
            } catch (error) {
                return { error: error.message };
            }
        }

        // 弹窗管理
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
            if (modalId === 'manageQuestionsModal' || modalId === 'fillSurveyModal' || modalId === 'statsModal') {
                loadSurveyOptions(modalId);
            }
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function openCreateSurveyModal() {
            openModal('createSurveyModal');
        }

        function openManageQuestionsModal() {
            openModal('manageQuestionsModal');
        }

        function openFillSurveyModal() {
            openModal('fillSurveyModal');
        }

        function openStatsModal() {
            openModal('statsModal');
        }

        // 点击弹窗外部关闭弹窗
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }

        // 加载问卷列表
        async function loadSurveys() {
            const loading = document.getElementById('loading');
            const surveyList = document.getElementById('surveyList');

            loading.style.display = 'block';
            surveyList.innerHTML = '';

            try {
                const result = await apiCall('/surveys');
                surveys = result;

                if (result && result.length > 0) {
                    surveyList.innerHTML = result.map(survey => `
                        <div class="survey-item">
                            <h4>${survey.surveyName}</h4>
                            <div class="meta">
                                <span class="status-badge ${survey.status === 1 ? 'status-active' : 'status-inactive'}">
                                    ${survey.status === 1 ? '启用' : '停用'}
                                </span>
                                <span style="margin-left: 15px;">ID: ${survey.id}</span>
                                <span style="margin-left: 15px;">参与人数: ${survey.participantCount || 0}</span>
                                <span style="margin-left: 15px;">创建时间: ${new Date(survey.createTime).toLocaleString()}</span>
                            </div>
                        </div>
                    `).join('');
                } else {
                    surveyList.innerHTML = '<div class="alert alert-info">暂无问卷数据</div>';
                }
            } catch (error) {
                surveyList.innerHTML = '<div class="alert alert-error">加载失败: ' + error.message + '</div>';
            } finally {
                loading.style.display = 'none';
            }
        }

        // 加载问卷选项到下拉框
        async function loadSurveyOptions(modalId) {
            if (surveys.length === 0) {
                await loadSurveys();
            }

            let selectElement;
            if (modalId === 'manageQuestionsModal') {
                selectElement = document.getElementById('questionSurveyId');
            } else if (modalId === 'fillSurveyModal') {
                selectElement = document.getElementById('fillSurveyId');
            } else if (modalId === 'statsModal') {
                selectElement = document.getElementById('statsSurveyId');
            }

            if (selectElement) {
                selectElement.innerHTML = '<option value="">请选择问卷</option>' +
                    surveys.map(survey => `<option value="${survey.id}">${survey.surveyName}</option>`).join('');
            }
        }

        // 创建问卷
        async function createSurvey() {
            const surveyName = document.getElementById('newSurveyName').value;
            const status = document.getElementById('surveyStatus').value;

            if (!surveyName.trim()) {
                alert('请输入问卷名称');
                return;
            }

            try {
                const data = {
                    surveyName: surveyName.trim(),
                    status: parseInt(status)
                };

                const result = await apiCall('/surveys', 'POST', data);

                if (result.id) {
                    showAlert('success', '问卷创建成功！');
                    closeModal('createSurveyModal');
                    document.getElementById('newSurveyName').value = '';
                    loadSurveys();
                } else {
                    showAlert('error', '创建失败: ' + (result.message || '未知错误'));
                }
            } catch (error) {
                showAlert('error', '创建失败: ' + error.message);
            }
        }

        // 添加问题
        async function addQuestion() {
            const surveyId = document.getElementById('questionSurveyId').value;
            const content = document.getElementById('questionContent').value;
            const questionType = document.getElementById('questionType').value;

            if (!surveyId) {
                alert('请选择问卷');
                return;
            }

            if (!content.trim()) {
                alert('请输入问题内容');
                return;
            }

            try {
                const data = {
                    surveyId: parseInt(surveyId),
                    configType: 1,
                    optionType: parseInt(questionType),
                    content: content.trim()
                };

                const result = await apiCall('/survey-configs', 'POST', data);

                if (result.id) {
                    showAlert('success', '问题添加成功！');
                    document.getElementById('questionContent').value = '';
                    loadQuestionsList(surveyId);
                    loadQuestionOptions(surveyId); // 刷新问题选项下拉框
                } else {
                    showAlert('error', '添加失败: ' + (result.message || '未知错误'));
                }
            } catch (error) {
                showAlert('error', '添加失败: ' + error.message);
            }
        }

        // 添加选项
        async function addOption() {
            const surveyId = document.getElementById('questionSurveyId').value;
            const questionId = document.getElementById('optionQuestionId').value;
            const content = document.getElementById('optionContent').value;

            if (!surveyId) {
                alert('请选择问卷');
                return;
            }

            if (!questionId) {
                alert('请选择问题');
                return;
            }

            if (!content.trim()) {
                alert('请输入选项内容');
                return;
            }

            try {
                const data = {
                    surveyId: parseInt(surveyId),
                    configType: 2,
                    questionId: parseInt(questionId),
                    optionType: null,
                    content: content.trim()
                };

                const result = await apiCall('/survey-configs', 'POST', data);

                if (result.id) {
                    showAlert('success', '选项添加成功！');
                    document.getElementById('optionContent').value = '';
                    loadQuestionsList(surveyId);
                } else {
                    showAlert('error', '添加失败: ' + (result.message || '未知错误'));
                }
            } catch (error) {
                showAlert('error', '添加失败: ' + error.message);
            }
        }

        // 加载问题选项到下拉框
        async function loadQuestionOptions(surveyId) {
            if (!surveyId) {
                document.getElementById('optionQuestionId').innerHTML = '<option value="">请先选择问卷并添加问题</option>';
                return;
            }

            try {
                const questions = await apiCall(`/survey-configs/survey/${surveyId}/questions`);
                const selectElement = document.getElementById('optionQuestionId');

                if (questions && questions.length > 0) {
                    selectElement.innerHTML = '<option value="">请选择问题</option>' +
                        questions.map(q => `<option value="${q.id}">${q.content}</option>`).join('');
                } else {
                    selectElement.innerHTML = '<option value="">暂无问题，请先添加问题</option>';
                }
            } catch (error) {
                document.getElementById('optionQuestionId').innerHTML = '<option value="">加载问题失败</option>';
            }
        }

        // 加载问题列表
        async function loadQuestionsList(surveyId) {
            if (!surveyId) return;

            try {
                const questions = await apiCall(`/survey-configs/survey/${surveyId}/questions`);

                const resultDiv = document.getElementById('questionResult');
                let html = '<h4>当前问题和选项：</h4>';

                if (questions && questions.length > 0) {
                    html += '<div style="margin-bottom: 20px;">';

                    for (const question of questions) {
                        const typeText = question.optionType === 1 ? '填空题' : question.optionType === 2 ? '单选题' : '多选题';
                        html += `<div style="padding: 15px; background: #f8f9fa; margin: 10px 0; border-radius: 8px; border-left: 4px solid #667eea;">
                            <div style="margin-bottom: 10px;">
                                <strong>${question.content}</strong>
                                <span style="color: #666; font-size: 0.9em;">(${typeText})</span>
                            </div>`;

                        // 如果是选择题，加载其选项
                        if (question.optionType === 2 || question.optionType === 3) {
                            try {
                                const options = await apiCall(`/survey-configs/question/${question.id}/options`);
                                if (options && options.length > 0) {
                                    html += '<div style="margin-left: 20px; margin-top: 10px;"><strong>选项：</strong>';
                                    options.forEach(option => {
                                        html += `<div style="padding: 5px 10px; background: #e9ecef; margin: 3px 0; border-radius: 3px; font-size: 0.9em;">
                                            • ${option.content}
                                        </div>`;
                                    });
                                    html += '</div>';
                                } else {
                                    html += '<div style="margin-left: 20px; color: #999; font-style: italic;">暂无选项</div>';
                                }
                            } catch (error) {
                                html += '<div style="margin-left: 20px; color: #dc3545; font-style: italic;">选项加载失败</div>';
                            }
                        }

                        html += '</div>';
                    }

                    html += '</div>';
                } else {
                    html += '<div class="alert alert-info">暂无问题</div>';
                }

                resultDiv.innerHTML = html;
            } catch (error) {
                document.getElementById('questionResult').innerHTML =
                    '<div class="alert alert-error">加载失败: ' + error.message + '</div>';
            }
        }

        // 加载问卷问题用于填写
        async function loadSurveyQuestions() {
            const surveyId = document.getElementById('fillSurveyId').value;
            const questionsDiv = document.getElementById('surveyQuestions');

            if (!surveyId) {
                questionsDiv.innerHTML = '';
                return;
            }

            try {
                const questions = await apiCall(`/survey-configs/survey/${surveyId}/questions`);

                if (!questions || questions.length === 0) {
                    questionsDiv.innerHTML = '<div class="alert alert-info">该问卷暂无问题</div>';
                    return;
                }

                let html = '<h4>问卷问题：</h4>';

                for (let i = 0; i < questions.length; i++) {
                    const question = questions[i];
                    html += `<div class="form-group" style="border: 1px solid #e0e0e0; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                        <label><strong>问题 ${i + 1}: ${question.content}</strong></label>
                        <input type="hidden" class="question-id" value="${question.id}">`;

                    if (question.optionType === 1) { // 填空题
                        html += `<input type="text" class="form-control answer-input" placeholder="请输入答案">`;
                    } else if (question.optionType === 2 || question.optionType === 3) { // 单选题或多选题
                        try {
                            const options = await apiCall(`/survey-configs/question/${question.id}/options`);
                            html += '<div style="margin-top: 10px;">';

                            if (options && options.length > 0) {
                                const inputType = question.optionType === 2 ? 'radio' : 'checkbox';
                                options.forEach(option => {
                                    html += `<label style="display: block; margin: 5px 0;">
                                        <input type="${inputType}" name="question_${question.id}" value="${option.content}" style="margin-right: 8px;">
                                        ${option.content}
                                    </label>`;
                                });
                            } else {
                                html += '<div style="color: #999; font-style: italic;">该问题暂无选项</div>';
                            }
                            html += '</div>';
                        } catch (error) {
                            html += '<div style="color: #dc3545; margin-top: 10px;">选项加载失败</div>';
                        }
                    }
                    html += '</div>';
                }

                questionsDiv.innerHTML = html;
            } catch (error) {
                questionsDiv.innerHTML = '<div class="alert alert-error">加载问题失败: ' + error.message + '</div>';
            }
        }

        // 提交所有答案
        async function submitAllAnswers() {
            const surveyId = document.getElementById('fillSurveyId').value;
            const userId = document.getElementById('userId').value;

            if (!surveyId) {
                alert('请选择问卷');
                return;
            }

            if (!userId) {
                alert('请输入用户ID');
                return;
            }

            const questionDivs = document.querySelectorAll('#surveyQuestions .form-group');
            const answers = [];

            questionDivs.forEach(div => {
                const questionId = div.querySelector('.question-id').value;
                const textInput = div.querySelector('.answer-input');
                const radioInputs = div.querySelectorAll('input[type="radio"]:checked');
                const checkboxInputs = div.querySelectorAll('input[type="checkbox"]:checked');

                let answer = '';

                if (textInput) {
                    answer = textInput.value.trim();
                } else if (radioInputs.length > 0) {
                    answer = radioInputs[0].value;
                } else if (checkboxInputs.length > 0) {
                    answer = Array.from(checkboxInputs).map(cb => cb.value).join(', ');
                }

                if (answer) {
                    answers.push({
                        surveyId: parseInt(surveyId),
                        userId: parseInt(userId),
                        questionId: parseInt(questionId),
                        answer: answer
                    });
                }
            });

            if (answers.length === 0) {
                alert('请至少回答一个问题');
                return;
            }

            try {
                const results = [];
                for (const answerData of answers) {
                    const result = await apiCall('/answers', 'POST', answerData);
                    results.push(result);
                }

                showAlert('success', `成功提交 ${results.length} 个答案！`);
                document.getElementById('fillResult').innerHTML =
                    '<div class="alert alert-success">答案提交成功！</div>';

                // 清空表单
                document.getElementById('userId').value = '';
                document.getElementById('surveyQuestions').innerHTML = '';
                document.getElementById('fillSurveyId').value = '';

            } catch (error) {
                showAlert('error', '提交失败: ' + error.message);
            }
        }

        // 获取问卷答案
        async function getAnswers() {
            const surveyId = document.getElementById('statsSurveyId').value;
            if (!surveyId) {
                alert('请选择问卷');
                return;
            }

            try {
                const result = await apiCall(`/answers/survey/${surveyId}`);
                const statsDiv = document.getElementById('statsResult');

                if (result && result.length > 0) {
                    let html = '<h4>答案列表：</h4>';
                    html += '<div style="max-height: 400px; overflow-y: auto;">';

                    result.forEach((answer, index) => {
                        html += `<div style="border: 1px solid #e0e0e0; padding: 15px; margin: 10px 0; border-radius: 8px; background: #f8f9fa;">
                            <div><strong>答案 ${index + 1}</strong></div>
                            <div>用户ID: ${answer.userId}</div>
                            <div>问题ID: ${answer.questionId}</div>
                            <div>答案: ${answer.answer}</div>
                            <div style="color: #666; font-size: 0.9em;">提交时间: ${new Date(answer.createTime).toLocaleString()}</div>
                        </div>`;
                    });

                    html += '</div>';
                    statsDiv.innerHTML = html;
                } else {
                    statsDiv.innerHTML = '<div class="alert alert-info">暂无答案数据</div>';
                }
            } catch (error) {
                document.getElementById('statsResult').innerHTML =
                    '<div class="alert alert-error">获取答案失败: ' + error.message + '</div>';
            }
        }

        // 获取参与人数统计
        async function getParticipantCount() {
            const surveyId = document.getElementById('statsSurveyId').value;
            if (!surveyId) {
                alert('请选择问卷');
                return;
            }

            try {
                const result = await apiCall(`/answers/survey/${surveyId}/participants/count`);
                const statsDiv = document.getElementById('statsResult');

                statsDiv.innerHTML = `
                    <div class="alert alert-success">
                        <h4>📊 参与统计</h4>
                        <div style="font-size: 1.2em; margin-top: 10px;">
                            <strong>参与人数: ${result.count || 0} 人</strong>
                        </div>
                    </div>
                `;
            } catch (error) {
                document.getElementById('statsResult').innerHTML =
                    '<div class="alert alert-error">获取统计失败: ' + error.message + '</div>';
            }
        }

        // 显示提示信息
        function showAlert(type, message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            alertDiv.style.position = 'fixed';
            alertDiv.style.top = '20px';
            alertDiv.style.right = '20px';
            alertDiv.style.zIndex = '9999';
            alertDiv.style.minWidth = '300px';
            alertDiv.style.animation = 'slideInRight 0.3s ease';

            document.body.appendChild(alertDiv);

            setTimeout(() => {
                alertDiv.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.parentNode.removeChild(alertDiv);
                    }
                }, 300);
            }, 3000);
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // 监听问卷选择变化，自动加载问题列表和选项
        document.addEventListener('change', function(e) {
            if (e.target.id === 'questionSurveyId') {
                const surveyId = e.target.value;
                loadQuestionsList(surveyId);
                loadQuestionOptions(surveyId);
            }
        });
    </script>
</body>
</html>
