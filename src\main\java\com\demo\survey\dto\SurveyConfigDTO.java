package com.demo.survey.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.Size;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SurveyConfigDTO {

    private Integer id;

    private Integer surveyId;

    private Integer configType; // 1问题,2选项

    private Integer optionType; // 问题类型:1填空,2单选,3多选

    private Integer questionId; // 选项所属的问题ID（仅当configType=2时使用）

    @Size(max = 255, message = "内容长度不能超过255个字符")
    private String content;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
