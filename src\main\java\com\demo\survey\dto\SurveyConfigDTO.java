package com.demo.survey.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.Size;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SurveyConfigDTO {

    private Integer id;

    private Integer surveyId;

    private Integer type; // 1问题,2选项

    @Size(max = 255, message = "内容长度不能超过255个字符")
    private String content;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
