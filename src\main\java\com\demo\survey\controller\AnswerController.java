package com.demo.survey.controller;

import com.demo.survey.dto.AnswerDTO;
import com.demo.survey.service.AnswerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/answers")
@CrossOrigin(origins = "*")
public class AnswerController {

    @Autowired
    private AnswerService answerService;

    @GetMapping("/survey/{surveyId}")
    public ResponseEntity<List<AnswerDTO>> getAnswersBySurveyId(@PathVariable Integer surveyId) {
        List<AnswerDTO> answers = answerService.getAnswersBySurveyId(surveyId);
        return ResponseEntity.ok(answers);
    }

    @GetMapping("/user/{userId}")
    public ResponseEntity<List<AnswerDTO>> getAnswersByUserId(@PathVariable Integer userId) {
        List<AnswerDTO> answers = answerService.getAnswersByUserId(userId);
        return ResponseEntity.ok(answers);
    }

    @GetMapping("/survey/{surveyId}/user/{userId}")
    public ResponseEntity<List<AnswerDTO>> getAnswersBySurveyIdAndUserId(
            @PathVariable Integer surveyId, 
            @PathVariable Integer userId) {
        List<AnswerDTO> answers = answerService.getAnswersBySurveyIdAndUserId(surveyId, userId);
        return ResponseEntity.ok(answers);
    }

    @PostMapping
    public ResponseEntity<AnswerDTO> createAnswer(@Valid @RequestBody AnswerDTO answerDTO) {
        AnswerDTO createdAnswer = answerService.createAnswer(answerDTO);
        return ResponseEntity.ok(createdAnswer);
    }

    @PutMapping("/{id}")
    public ResponseEntity<AnswerDTO> updateAnswer(@PathVariable Integer id, @Valid @RequestBody AnswerDTO answerDTO) {
        AnswerDTO updatedAnswer = answerService.updateAnswer(id, answerDTO);
        if (updatedAnswer != null) {
            return ResponseEntity.ok(updatedAnswer);
        }
        return ResponseEntity.notFound().build();
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteAnswer(@PathVariable Integer id) {
        boolean deleted = answerService.deleteAnswer(id);
        if (deleted) {
            return ResponseEntity.ok().build();
        }
        return ResponseEntity.notFound().build();
    }

    @GetMapping("/survey/{surveyId}/participants/count")
    public ResponseEntity<Long> getParticipantCount(@PathVariable Integer surveyId) {
        Long count = answerService.getParticipantCount(surveyId);
        return ResponseEntity.ok(count);
    }
}
